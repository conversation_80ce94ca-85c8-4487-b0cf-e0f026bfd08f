# 高级应用：将上下文工程付诸实践

> "理论上，理论和实践是一样的。实践中，它们不是。" — 阿尔伯特·爱因斯坦

## 超越基础：应用上下文工程

我们已经建立了上下文工程概念的坚实基础，从原子提示到认知工具。现在是时候看看这些原则如何应用于推动大语言模型可能性边界的现实世界挑战。

```
┌──────────────┐     ┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│              │     │              │     │              │     │              │
│    原子      │────►│    分子      │────►│    细胞      │────►│    器官      │
│   (提示)     │     │  (少样本)    │     │   (记忆)     │     │(多智能体)    │
│              │     │              │     │              │     │              │
└──────────────┘     └──────────────┘     └──────────────┘     └──────────────┘
       │                    │                   │                    │
       │                    │                   │                    │
       │                    │                   │                    │
       ▼                    ▼                   ▼                    ▼
┌──────────────────────────────────────────────────────────────────────────────┐
│                                                                              │
│                         高级应用                                             │
│                                                                              │
└──────────────────────────────────────────────────────────────────────────────┘
```

## 应用领域：长篇内容创作

创建长篇、连贯的内容推动了上下文管理的极限。让我们看看我们的原则如何应用：

```
┌───────────────────────────────────────────────────────────────────────────┐
│                    长篇内容创作                                           │
│                                                                           │
│  ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐      │
│  │                 │     │                 │     │                 │      │
│  │  内容           │────►│  章节           │────►│  渐进式         │      │
│  │  规划           │     │  生成           │     │  整合           │      │
│  │                 │     │                 │     │                 │      │
│  └─────────────────┘     └─────────────────┘     └─────────────────┘      │
│         │                       │                       │                 │
│         ▼                       ▼                       ▼                 │
│  ┌─────────────┐         ┌─────────────┐         ┌─────────────┐          │
│  │             │         │             │         │             │          │
│  │ 大纲        │         │ 章节        │         │ 连贯性      │          │
│  │ 图式        │         │ 模板        │         │ 验证        │          │
│  │             │         │             │         │             │          │
│  └─────────────┘         └─────────────┘         └─────────────┘          │
│                                                                           │
└───────────────────────────────────────────────────────────────────────────┘
```

### 实现：文档生成系统

```python
class LongFormGenerator:
    """用于生成连贯长篇内容的系统。"""
    
    def __init__(self, llm_service):
        self.llm = llm_service
        self.document_state = {
            "title": "",
            "outline": [],
            "sections": {},
            "current_section": "",
            "theme_keywords": [],
            "style_guide": {},
            "completed_sections": []
        }
    
    def create_outline(self, topic, length="medium", style="informative"):
        """为文档生成结构化大纲。"""
        # 大纲生成的提示程序示例
        outline_prompt = f"""
        任务：为关于{topic}的{length}{style}文档创建详细大纲。
        
        过程：
        1. 识别3-5个全面涵盖主题的主要部分
        2. 对于每个主要部分，识别2-4个子部分
        3. 为每个部分添加简要描述（1-2句话）说明将涵盖什么
        4. 包括部分之间的建议过渡
        
        格式：
        标题：[建议标题]
        
        主要部分：
        1. [部分标题]
           - 描述：[简要描述]
           - 子部分：
             a. [子部分标题]
             b. [子部分标题]
           - 过渡：[流向下一部分的建议]
        
        2. [继续模式...]
        
        主题关键词：[5-7个保持一致性的关键术语]
        语调指导：[3-4个风格建议]
        """
        
        outline_response = self.llm.generate(outline_prompt)
        self._parse_outline(outline_response)
        return self.document_state["outline"]
    
    def _parse_outline(self, outline_text):
        """将大纲响应解析为结构化格式。"""
        # 在真实实现中，这将提取结构化大纲
        # 为简单起见，我们将使用占位符实现
        self.document_state["title"] = "示例文档标题"
        self.document_state["outline"] = [
            {"title": "引言", "subsections": ["背景", "重要性"]},
            {"title": "主要部分1", "subsections": ["子主题A", "子主题B"]},
            {"title": "主要部分2", "subsections": ["子主题C", "子主题D"]},
            {"title": "结论", "subsections": ["总结", "未来方向"]}
        ]
        self.document_state["theme_keywords"] = ["关键词1", "关键词2", "关键词3"]
        self.document_state["style_guide"] = {
            "tone": "信息性",
            "perspective": "第三人称",
            "style_notes": "使用具体例子"
        }
    
    def generate_section(self, section_index):
        """为特定部分生成内容。"""
        section = self.document_state["outline"][section_index]
        self.document_state["current_section"] = section["title"]
        
        # 创建上下文感知的部分提示
        context = self._build_section_context(section_index)
        
        section_prompt = f"""
        任务：撰写标题为"{self.document_state["title"]}"文档的"{section["title"]}"部分。
        
        上下文：
        {context}
        
        指导原则：
        - 与文档主题和之前部分保持一致性
        - 涵盖所有子部分：{", ".join(section["subsections"])}
        - 保持{self.document_state["style_guide"]["tone"]}语调
        - 从{self.document_state["style_guide"]["perspective"]}视角写作
        - {self.document_state["style_guide"]["style_notes"]}
        
        格式：
        ## {section["title"]}
        
        [涵盖所有子部分的内容，大约300-500字]
        """
        
        section_content = self.llm.generate(section_prompt)
        self.document_state["sections"][section["title"]] = section_content
        self.document_state["completed_sections"].append(section["title"])
        
        return section_content
    
    def _build_section_context(self, section_index):
        """为生成部分构建相关上下文。"""
        context = "之前的部分：\n"
        
        # 包括之前写过的部分的摘要以提供上下文
        for title in self.document_state["completed_sections"]:
            # 实际上，你会包括摘要而不是全文以节省令牌
            content = self.document_state["sections"].get(title, "")
            summary = content[:100] + "..." if len(content) > 100 else content
            context += f"- {title}：{summary}\n"
        
        # 包括主题关键词以保持一致性
        context += "\n主题关键词：" + ", ".join(self.document_state["theme_keywords"])
        
        # 位置信息（开头、中间、结尾）
        total_sections = len(self.document_state["outline"])
        if section_index == 0:
            context += "\n这是文档的开头部分。"
        elif section_index == total_sections - 1:
            context += "\n这是文档的结尾部分。"
        else:
            context += f"\n这是{total_sections}个部分中的第{section_index + 1}部分。"
        
        return context
    
    def verify_coherence(self, section_index):
        """验证并改善与之前部分的连贯性。"""
        if section_index == 0:
            return "第一部分 - 不需要连贯性检查。"
        
        section = self.document_state["outline"][section_index]
        previous_section = self.document_state["outline"][section_index - 1]
        
        current_content = self.document_state["sections"][section["title"]]
        previous_content = self.document_state["sections"][previous_section["title"]]
        
        # 使用专门的提示程序进行连贯性验证
        coherence_prompt = f"""
        任务：验证并改善两个连续文档部分之间的连贯性。
        
        之前部分：{previous_section["title"]}
        {previous_content[-200:]}  # 之前部分的最后部分
        
        当前部分：{section["title"]}
        {current_content[:200]}  # 当前部分的开头部分
        
        过程：
        1. 识别任何主题或逻辑断裂
        2. 检查重复或矛盾
        3. 验证过渡是否流畅
        4. 确保术语和风格一致
        
        格式：
        连贯性评估：[良好/需要改进]
        
        发现的问题：
        1. [问题1，如果有的话]
        2. [问题2，如果有的话]
        
        改进建议：
        [改善连接的具体建议]
        """
        
        assessment = self.llm.generate(coherence_prompt)
        
        # 在完整实现中，你会解析评估并应用改进
        return assessment
    
    def generate_complete_document(self):
        """逐部分生成整个文档。"""
        # 首先，确保我们有大纲
        if not self.document_state["outline"]:
            raise ValueError("必须先创建大纲")
        
        # 按顺序生成每个部分
        all_content = [f"# {self.document_state['title']}\n\n"]
        
        for i in range(len(self.document_state["outline"])):
            section_content = self.generate_section(i)
            
            # 对于第一部分之后的部分，验证连贯性
            if i > 0:
                coherence_check = self.verify_coherence(i)
                # 实际上，你会使用这个来改进部分
            
            all_content.append(section_content)
        
        # 合并所有部分
        return "\n\n".join(all_content)
```

这个实现演示了：
1. **结构化内容规划**使用提示程序
2. **渐进式上下文构建**随着部分的生成
3. **连贯性验证**在相邻部分之间
4. **状态管理**贯穿整个文档创建过程

## 应用领域：带记忆的复杂推理

复杂推理通常需要在多个步骤中跟踪状态，同时保留关键洞察：

```
┌───────────────────────────────────────────────────────────────────────────┐
│                      复杂推理系统                                         │
│                                                                           │
│  ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐      │
│  │                 │     │                 │     │                 │      │
│  │  问题           │────►│  解决方案       │────►│  验证与         │      │
│  │  分析           │     │  生成           │     │  完善           │      │
│  │                 │     │                 │     │                 │      │
│  └─────────────────┘     └─────────────────┘     └─────────────────┘      │
│         │                       │                       │                 │
│         ▼                       ▼                       ▼                 │
│  ┌─────────────┐         ┌─────────────┐         ┌─────────────┐          │
│  │             │         │             │         │             │          │
│  │ 结构化      │         │ 思维链      │         │ 自我        │          │
│  │ 问题        │         │ 模板        │         │ 纠错        │          │
│  │ 图式        │         │             │         │ 循环        │          │
│  │             │         │             │         │             │          │
│  └─────────────┘         └─────────────┘         └─────────────┘          │
│                                                                           │
└───────────────────────────────────────────────────────────────────────────┘
```

### 实现：数学问题求解器

```python
class MathProblemSolver:
    """逐步解决复杂数学问题的系统。"""

    def __init__(self, llm_service):
        self.llm = llm_service
        self.problem_state = {
            "original_problem": "",
            "parsed_problem": {},
            "solution_steps": [],
            "current_step": 0,
            "verification_results": [],
            "final_answer": ""
        }

    def parse_problem(self, problem_text):
        """解析并结构化数学问题。"""
        # 基于图式的问题解析
        parse_prompt = f"""
        任务：分析并结构化以下数学问题。

        问题：{problem_text}

        过程：
        1. 识别问题类型（代数、微积分、几何等）
        2. 提取相关变量及其关系
        3. 识别约束和条件
        4. 确定要求解的内容

        格式：
        问题类型：[类型]

        变量：
        - [变量1]：[描述]
        - [变量2]：[描述]

        关系：
        - [方程或关系1]
        - [方程或关系2]

        约束：
        - [约束1]
        - [约束2]

        目标：[需要找到什么]

        建议方法：[解决方法的简要概述]
        """

        parse_result = self.llm.generate(parse_prompt)
        self.problem_state["original_problem"] = problem_text

        # 实际上，你会解析结构化输出
        # 为简单起见，我们将使用占位符实现
        self.problem_state["parsed_problem"] = {
            "type": "代数",
            "variables": {"x": "未知值", "y": "依赖值"},
            "relationships": ["y = 2x + 3"],
            "constraints": ["x > 0"],
            "goal": "当y = 15时求x",
            "approach": "代入y = 15并求解x"
        }

        return self.problem_state["parsed_problem"]

    def generate_solution_step(self):
        """生成解决过程中的下一步。"""
        # 从之前的步骤构建上下文
        context = self._build_step_context()

        step_prompt = f"""
        任务：生成解决这个数学问题的下一步。

        原始问题：{self.problem_state["original_problem"]}

        问题分析：
        类型：{self.problem_state["parsed_problem"]["type"]}
        目标：{self.problem_state["parsed_problem"]["goal"]}

        之前的步骤：
        {context}

        过程：
        1. 考虑之前步骤中已完成的内容
        2. 确定下一个逻辑操作
        3. 执行该操作，显示所有工作
        4. 解释数学推理

        格式：
        步骤{self.problem_state["current_step"] + 1}：[简要描述]

        操作：[执行的数学操作]

        工作：
        [逐步计算]

        解释：
        [为什么这一步是必要的以及它完成了什么]

        状态：[完成/需要更多步骤]
        """

        step_result = self.llm.generate(step_prompt)
        self.problem_state["solution_steps"].append(step_result)
        self.problem_state["current_step"] += 1

        # 检查这一步是否包含最终答案
        if "状态：完成" in step_result:
            # 提取最终答案（实际上，你会更仔细地解析这个）
            self.problem_state["final_answer"] = "x = 6"

        return step_result

    def _build_step_context(self):
        """从之前的解决步骤构建上下文。"""
        if not self.problem_state["solution_steps"]:
            return "没有之前的步骤。这是解决方案的开始。"

        # 在上下文中包括所有之前的步骤
        # 实际上，你可能需要为令牌限制进行总结或截断
        context = "之前的步骤：\n"
        for i, step in enumerate(self.problem_state["solution_steps"]):
            context += f"步骤{i+1}：{step[:200]}...\n"

        return context

    def verify_step(self, step_index):
        """验证特定解决步骤的正确性。"""
        if step_index >= len(self.problem_state["solution_steps"]):
            return "步骤索引超出范围"

        step = self.problem_state["solution_steps"][step_index]

        # 使用专门的验证提示
        verify_prompt = f"""
        任务：验证这个数学解决步骤的正确性。

        原始问题：{self.problem_state["original_problem"]}

        要验证的步骤：
        {step}

        过程：
        1. 检查数学操作的准确性
        2. 验证逻辑是否遵循之前的步骤
        3. 确保解释与显示的工作匹配
        4. 寻找常见错误或误解

        格式：
        正确性：[正确/不正确/部分正确]

        发现的问题：
        - [问题1，如果有的话]
        - [问题2，如果有的话]

        建议纠正：
        [如何修复识别出的任何问题]
        """

        verification = self.llm.generate(verify_prompt)
        self.problem_state["verification_results"].append(verification)

        return verification

    def solve_complete_problem(self, problem_text, max_steps=10):
        """通过验证逐步解决完整问题。"""
        # 解析问题
        self.parse_problem(problem_text)

        # 生成并验证步骤直到解决方案完成
        while self.problem_state["final_answer"] == "" and self.problem_state["current_step"] < max_steps:
            # 生成下一步
            step = self.generate_solution_step()

            # 验证步骤
            verification = self.verify_step(self.problem_state["current_step"] - 1)

            # 如果验证发现问题，你可能会重新生成步骤
            # 这是一个简化的实现
            if "正确性：不正确" in verification:
                # 实际上，你会使用反馈来改进步骤
                print(f"步骤{self.problem_state['current_step']}有问题。为了这个例子继续。")

        # 返回完整解决方案
        return {
            "problem": self.problem_state["original_problem"],
            "steps": self.problem_state["solution_steps"],
            "verifications": self.problem_state["verification_results"],
            "final_answer": self.problem_state["final_answer"]
        }
```

这个实现演示了：
1. **结构化问题解析**使用基于图式的方法
2. **逐步推理**具有明确的中间状态
3. **自我验证**在每个阶段检查工作
4. **记忆管理**在整个解决过程中维护上下文

## 应用领域：知识综合

从多个来源综合信息需要复杂的上下文管理：

```
┌───────────────────────────────────────────────────────────────────────────┐
│                      知识综合系统                                         │
│                                                                           │
│  ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐      │
│  │                 │     │                 │     │                 │      │
│  │  信息           │────►│  概念           │────►│  整合与         │      │
│  │  检索           │     │  提取           │     │  综合           │      │
│  │                 │     │                 │     │                 │      │
│  └─────────────────┘     └─────────────────┘     └─────────────────┘      │
│         │                       │                       │                 │
│         ▼                       ▼                       ▼                 │
│  ┌─────────────┐         ┌─────────────┐         ┌─────────────┐          │
│  │             │         │             │         │             │          │
│  │ 检索        │         │ 知识        │         │ 比较        │          │
│  │ 查询        │         │ 图谱        │         │ 矩阵        │          │
│  │ 模板        │         │ 图式        │         │ 模板        │          │
│  │             │         │             │         │             │          │
│  └─────────────┘         └─────────────┘         └─────────────┘          │
│                                                                           │
└───────────────────────────────────────────────────────────────────────────┘
```

### 实现：研究助手

```python
class ResearchAssistant:
    """从多个来源综合信息的系统。"""

    def __init__(self, llm_service, retrieval_service):
        self.llm = llm_service
        self.retrieval = retrieval_service
        self.research_state = {
            "topic": "",
            "query_results": [],
            "extracted_concepts": {},
            "concept_relationships": [],
            "synthesis": "",
            "knowledge_gaps": []
        }

    def set_research_topic(self, topic):
        """设置研究主题并生成初始查询。"""
        self.research_state["topic"] = topic

        # 使用提示程序生成结构化查询
        query_prompt = f"""
        任务：为研究主题"{topic}"生成有效的搜索查询

        过程：
        1. 将主题分解为其核心组件
        2. 对于每个组件，生成特定的搜索查询
        3. 包括对主题不同观点的查询
        4. 添加背景/基础信息的查询

        格式：
        核心组件：
        - [组件1]
        - [组件2]

        推荐查询：
        1. [具体查询1]
        2. [具体查询2]
        3. [具体查询3]

        观点查询：
        1. [观点1的查询]
        2. [观点2的查询]

        背景查询：
        1. [背景1的查询]
        2. [背景2的查询]
        """

        query_suggestions = self.llm.generate(query_prompt)

        # 实际上，你会解析结构化输出
        # 对于这个例子，我们将使用占位符查询
        return ["查询1", "查询2", "查询3"]

    def retrieve_information(self, queries):
        """使用生成的查询检索信息。"""
        # 在真实实现中，这会调用实际的检索服务
        # 对于这个例子，我们将使用占位符结果
        for query in queries:
            # 模拟检索结果
            results = [
                {"title": f"{query}的结果1", "content": "示例内容1", "source": "来源A"},
                {"title": f"{query}的结果2", "content": "示例内容2", "source": "来源B"}
            ]
            self.research_state["query_results"].extend(results)

        return self.research_state["query_results"]

    def extract_concepts(self):
        """从检索的信息中提取关键概念。"""
        # 从检索结果构建上下文
        context = self._build_retrieval_context()

        # 使用基于图式的概念提取提示
        concept_prompt = f"""
        任务：从以下研究信息中提取关键概念。

        研究主题：{self.research_state["topic"]}

        信息来源：
        {context}

        过程：
        1. 识别多个来源中提到的关键概念
        2. 对于每个概念，提取相关细节和定义
        3. 注意概念描述的变化或分歧
        4. 为每个概念分配相关性分数（1-10）

        格式：
        概念：[概念名称1]
        定义：[综合定义]
        关键属性：
        - [属性1]
        - [属性2]
        来源变化：
        - [来源A]：[这个来源如何描述它]
        - [来源B]：[这个来源如何描述它]
        相关性分数：[1-10]

        概念：[概念名称2]
        ...
        """

        extraction_results = self.llm.generate(concept_prompt)

        # 实际上，你会解析结构化输出
        # 对于这个例子，我们将使用占位符概念
        self.research_state["extracted_concepts"] = {
            "概念1": {
                "definition": "概念1的定义",
                "properties": ["属性1", "属性2"],
                "source_variations": {
                    "来源A": "来源A的描述",
                    "来源B": "来源B的描述"
                },
                "relevance": 8
            },
            "概念2": {
                "definition": "概念2的定义",
                "properties": ["属性1", "属性2"],
                "source_variations": {
                    "来源A": "来源A的描述",
                    "来源B": "来源B的描述"
                },
                "relevance": 7
            }
        }

        return self.research_state["extracted_concepts"]

    def _build_retrieval_context(self):
        """从检索结果构建上下文。"""
        if not self.research_state["query_results"]:
            return "尚未检索到信息。"

        # 包括检索信息的样本
        # 实际上，你可能需要为令牌限制进行总结或选择
        context = ""
        for i, result in enumerate(self.research_state["query_results"][:5]):
            context += f"来源{i+1}：{result['title']}\n"
            context += f"内容：{result['content'][:200]}...\n"
            context += f"来源：{result['source']}\n\n"

        return context

    def analyze_relationships(self):
        """分析提取概念之间的关系。"""
        if not self.research_state["extracted_concepts"]:
            return "尚未提取概念。"

        # 获取概念名称列表
        concepts = list(self.research_state["extracted_concepts"].keys())

        # 使用比较矩阵模板进行关系分析
        relationship_prompt = f"""
        任务：分析研究主题中关键概念之间的关系。

        研究主题：{self.research_state["topic"]}

        要分析的概念：
        {", ".join(concepts)}

        过程：
        1. 在所有概念之间创建关系矩阵
        2. 对于每一对，确定关系类型
        3. 注意每个关系的强度（1-5）
        4. 识别任何冲突或互补关系

        格式：
        关系矩阵：

        | 概念 | {" | ".join(concepts)} |
        |------|{"-|" * len(concepts)}
        """

        # 为每个概念添加行
        for concept in concepts:
            relationship_prompt += f"| {concept} |"
            for other in concepts:
                if concept == other:
                    relationship_prompt += " X |"
                else:
                    relationship_prompt += " ? |"
            relationship_prompt += "\n"

        relationship_prompt += """

        详细关系：

        [概念A] → [概念B]
        类型：[因果/层次/相关等]
        强度：[1-5]
        描述：[它们如何相关的简要描述]

        [继续其他相关对...]
        """

        relationship_results = self.llm.generate(relationship_prompt)

        # 实际上，你会解析结构化输出
        # 对于这个例子，我们将使用占位符关系
        self.research_state["concept_relationships"] = [
            {
                "source": "概念1",
                "target": "概念2",
                "type": "因果",
                "strength": 4,
                "description": "概念1直接影响概念2"
            }
        ]

        return self.research_state["concept_relationships"]

    def synthesize_research(self):
        """综合全面的研究摘要。"""
        # 确保我们已经提取了概念和关系
        if not self.research_state["extracted_concepts"]:
            self.extract_concepts()

        if not self.research_state["concept_relationships"]:
            self.analyze_relationships()

        # 从概念和关系构建上下文
        concepts_str = json.dumps(self.research_state["extracted_concepts"], indent=2, ensure_ascii=False)
        relationships_str = json.dumps(self.research_state["concept_relationships"], indent=2, ensure_ascii=False)

        synthesis_prompt = f"""
        任务：综合关于该主题的全面研究摘要。

        研究主题：{self.research_state["topic"]}

        关键概念：
        {concepts_str}

        概念关系：
        {relationships_str}

        过程：
        1. 创建整合关键概念的连贯叙述
        2. 突出来源间的共识领域
        3. 注意重要分歧或矛盾
        4. 识别知识空白或需要进一步研究的领域
        5. 总结最重要的发现

        格式：
        # 研究综合：[主题]

        ## 关键发现
        [最重要洞察的摘要]

        ## 概念整合
        [连接概念及其关系的叙述]

        ## 共识领域
        [来源一致的要点]

        ## 分歧领域
        [来源不一致或矛盾的要点]

        ## 知识空白
        [需要更多研究的领域]

        ## 结论
        [对当前知识状态的总体评估]
        """

        synthesis = self.llm.generate(synthesis_prompt)
        self.research_state["synthesis"] = synthesis

        # 提取知识空白（实际上，你会从综合中解析这些）
        self.research_state["knowledge_gaps"] = [
            "空白1：需要对X进行更多研究",
            "空白2：Y和Z之间的关系不清楚"
        ]

        return synthesis

    def complete_research_cycle(self, topic):
        """运行从主题到综合的完整研究周期。"""
        # 设置研究主题并生成查询
        queries = self.set_research_topic(topic)

        # 检索信息
        self.retrieve_information(queries)

        # 提取和分析概念
        self.extract_concepts()
        self.analyze_relationships()

        # 综合研究发现
        synthesis = self.synthesize_research()

        return {
            "topic": topic,
            "synthesis": synthesis,
            "concepts": self.research_state["extracted_concepts"],
            "relationships": self.research_state["concept_relationships"],
            "knowledge_gaps": self.research_state["knowledge_gaps"]
        }
```

这个实现演示了：
1. **结构化查询生成**以检索相关信息
2. **基于图式的概念提取**以识别关键思想
3. **关系分析**使用比较矩阵方法
4. **知识综合**将概念整合成连贯叙述

## 高级应用的关键模式

在这些不同的应用中，我们可以识别出增强上下文工程有效性的常见模式：

```
┌───────────────────────────────────────────────────────────────────┐
│ 高级上下文工程模式                                                │
├───────────────────────────────────────────────────────────────────┤
│ ◆ 状态管理：跨交互跟踪复杂状态                                    │
│ ◆ 渐进式上下文：增量构建上下文                                    │
│ ◆ 验证循环：质量和准确性的自我检查                                │
│ ◆ 结构化图式：系统性组织信息                                      │
│ ◆ 模板程序：特定任务的可重用提示模式                              │
│ ◆ 个性化：适应用户需求和上下文                                    │
│ ◆ 多步处理：将复杂任务分解为阶段                                  │
└───────────────────────────────────────────────────────────────────┘
```

## 测量应用性能

与简单的上下文结构一样，测量对于高级应用仍然至关重要：

```
┌───────────────────────────────────────────────────────────────────┐
│ 高级应用的测量维度                                                │
├──────────────────────────────┬────────────────────────────────────┤
│ 维度                         │ 指标                               │
├──────────────────────────────┼────────────────────────────────────┤
│ 端到端质量                   │ 准确性、正确性、连贯性             │
├──────────────────────────────┼────────────────────────────────────┤
│ 效率                         │ 总令牌数、完成时间                 │
├──────────────────────────────┼────────────────────────────────────┤
│ 鲁棒性                       │ 错误恢复率、边缘情况处理           │
├──────────────────────────────┼────────────────────────────────────┤
│ 用户满意度                   │ 相关性、个性化准确性               │
├──────────────────────────────┼────────────────────────────────────┤
│ 自我改进                     │ 随时间的错误减少                   │
└──────────────────────────────┴────────────────────────────────────┘
```

## 关键要点

1. **高级应用**建立在上下文工程的基本原则之上
2. **状态管理**对于复杂应用变得越来越重要
3. **基于图式的方法**为处理复杂信息提供结构
4. **多步处理**将复杂任务分解为可管理的部分
5. **自我验证**提高可靠性和准确性
6. **测量仍然至关重要**用于优化应用性能

## 实践练习

1. 扩展其中一个示例实现，添加额外功能
2. 在你的领域中实现应用的简化版本
3. 为你工作的特定类型信息设计图式
4. 为你的应用创建测量框架

## 下一步

在下一节中，我们将探索提示编程——一种将编程结构与提示灵活性相结合的强大方法，以创建更复杂的上下文工程解决方案。

[继续到 07_prompt_programming.md →](07_prompt_programming.md)

---

## 深入探讨：工程权衡

高级应用需要平衡几个竞争因素：

```
┌──────────────────────────────────────────────────────────────────┐
│ 上下文工程权衡                                                   │
├──────────────────────────────────────────────────────────────────┤
│ ◆ 复杂性 vs. 可维护性                                            │
│   更复杂的系统可能更难调试和维护                                 │
│                                                                  │
│ ◆ 令牌使用 vs. 质量                                              │
│   更多上下文通常提高质量但增加成本                               │
│                                                                  │
│ ◆ 专用 vs. 通用                                                  │
│   专用组件工作更好但可重用性较差                                 │
│                                                                  │
│ ◆ 严格结构 vs. 灵活性                                            │
│   结构化图式提高一致性但降低适应性                               │
└──────────────────────────────────────────────────────────────────┘
```

为你的特定应用找到正确的平衡是高级上下文工程的关键部分。
