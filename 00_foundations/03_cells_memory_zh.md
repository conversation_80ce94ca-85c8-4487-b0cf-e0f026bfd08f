# 细胞：添加记忆和状态

> "我们就是我们的记忆，我们是那个变幻形状的奇幻博物馆，那堆破碎的镜子。" — 豪尔赫·路易斯·博尔赫斯

## 从分子到细胞

我们已经探索了**原子**（单一指令）和**分子**（带示例的指令）。现在我们上升到**细胞** — 具有在多次交互中持续存在的**记忆**的上下文结构。

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  细胞 = [指令] + [示例] + [记忆/状态] + [当前输入]                           │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

就像生物细胞在与环境交互时维持其内部状态一样，我们的上下文"细胞"在与LLM的多次交换中保存信息。

## 记忆问题

默认情况下，LLM没有记忆。每个请求都是独立处理的：

```
┌───────────────────────┐      ┌───────────────────────┐
│ 请求 1                │      │ 请求 2                │
├───────────────────────┤      ├───────────────────────┤
│ "我的名字是Alex。"    │      │ "我的名字是什么？"    │
│                       │      │                       │
│                       │      │                       │
└───────────────────────┘      └───────────────────────┘
          │                              │
          ▼                              ▼
┌───────────────────────┐      ┌───────────────────────┐
│ 响应 1                │      │ 响应 2                │
├───────────────────────┤      ├───────────────────────┤
│ "你好Alex，很高兴     │      │ "我无法访问之前的     │
│  认识你。"            │      │  对话..."             │
│                       │      │                       │
└───────────────────────┘      └───────────────────────┘
```

没有记忆，LLM会忘记之前交互的信息，创造出脱节、令人沮丧的用户体验。

## 细胞解决方案：对话记忆

最简单的细胞结构将对话历史添加到上下文中：

```
┌───────────────────────────────────────────────────────────────────────┐
│                                                                       │
│  系统提示："你是一个有用的助手..."                                    │
│                                                                       │
│  对话历史：                                                           │
│  用户："我的名字是Alex。"                                             │
│  助手："你好Alex，很高兴认识你。"                                     │
│                                                                       │
│  当前输入："我的名字是什么？"                                         │
│                                                                       │
└───────────────────────────────────────────────────────────────────────┘
```

现在LLM可以访问之前的交换并保持连续性。

## 记忆令牌预算问题

随着对话增长，上下文窗口被填满。我们需要记忆管理策略：

```
          [上下文窗口令牌]
          ┌─────────────────────────────────────────────┐
          │                                             │
轮次 1    │ 系统指令           用户输入 1                │
          │                                             │
          ├─────────────────────────────────────────────┤
          │                                             │
轮次 2    │ 系统    历史 1     用户输入 2                │
          │                                             │
          ├─────────────────────────────────────────────┤
          │                                             │
轮次 3    │ 系统  历史 1  历史 2  用户输入 3             │
          │                                             │
          ├─────────────────────────────────────────────┤
          │                                             │
轮次 4    │ 系  历史 1-3               用户输入 4        │
          │                                             │
          ├─────────────────────────────────────────────┤
          │                                             │
轮次 5    │ 历史 2-4                   用户输入 5        │
          │                                             │
          └─────────────────────────────────────────────┘
                                       ▲
                                       │
                        最终，某些东西必须被删除
```

## 记忆管理策略

几种策略有助于优化有限上下文窗口的使用：

```
┌───────────────────────────────────────────────────────────────────┐
│ 记忆管理策略                                                      │
├────────────────────┬──────────────────────────────────────────────┤
│ 窗口化             │ 只保留最近的N轮                              │
├────────────────────┼──────────────────────────────────────────────┤
│ 摘要化             │ 将较旧的轮次压缩为摘要                       │
├────────────────────┼──────────────────────────────────────────────┤
│ 键值存储           │ 单独提取和存储重要事实                       │
├────────────────────┼──────────────────────────────────────────────┤
│ 优先级修剪         │ 首先删除不太重要的轮次                       │
├────────────────────┼──────────────────────────────────────────────┤
│ 语义分块           │ 将相关交换组合在一起                         │
└────────────────────┴──────────────────────────────────────────────┘
```

## 窗口化：滑动上下文

最简单的记忆管理方法只保留最近的对话轮次：

```
                    ┌───────────────────────────┐
轮次 1              │ 系统 + 轮次 1             │
                    └───────────────────────────┘
                              │
                              ▼
                    ┌───────────────────────────┐
轮次 2              │ 系统 + 轮次 1-2           │
                    └───────────────────────────┘
                              │
                              ▼
                    ┌───────────────────────────┐
轮次 3              │ 系统 + 轮次 1-3           │
                    └───────────────────────────┘
                              │
                              ▼
                    ┌───────────────────────────┐
轮次 4              │ 系统 + 轮次 2-4           │ ← 轮次 1 被丢弃
                    └───────────────────────────┘
                              │
                              ▼
                    ┌───────────────────────────┐
轮次 5              │ 系统 + 轮次 3-5           │ ← 轮次 2 被丢弃
                    └───────────────────────────┘
```

这种方法简单但会忘记早期轮次的信息。

## 摘要化：压缩记忆

更复杂的方法将较旧的轮次压缩为摘要：

```
                    ┌────────────────────────────────────────────┐
轮次 1-3            │ 系统 + 轮次 1-3                            │
                    └────────────────────────────────────────────┘
                                       │
                                       ▼
                    ┌────────────────────────────────────────────┐
轮次 4              │ 系统 + 摘要(轮次 1-2) + 轮次 3-4           │
                    └────────────────────────────────────────────┘
                                       │
                                       ▼
                    ┌────────────────────────────────────────────┐
轮次 5              │ 系统 + 摘要(轮次 1-3) + 轮次 4-5           │
                    └────────────────────────────────────────────┘
```

摘要化在减少令牌数量的同时保留关键信息。

## 键值记忆：结构化状态

为了更好的控制，我们可以以结构化格式提取和存储重要事实：

```
┌─────────────────────────────────────────────────────────────────────┐
│ 上下文窗口                                                          │
│                                                                     │
│  系统提示："你是一个有用的助手..."                                  │
│                                                                     │
│  记忆：                                                             │
│  {                                                                  │
│    "用户姓名": "Alex",                                              │
│    "喜欢的颜色": "蓝色",                                            │
│    "位置": "多伦多",                                                │
│    "最后话题": "度假计划"                                           │
│  }                                                                  │
│                                                                     │
│  最近对话：                                                         │
│  用户："你会推荐什么活动？"                                         │
│  助手："考虑到你在多伦多的位置和对...的兴趣"                        │
│                                                                     │
│  当前输入："室内活动怎么样？外面很冷。"                             │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

这种结构化方法允许精确控制保留哪些信息。

## 超越对话：有状态应用

细胞不仅仅能实现连贯的对话。它们允许有状态的应用，其中LLM：

1. 记住之前的交互
2. 更新和维护变量
3. 跟踪多步骤过程的进度
4. 基于之前的输出构建

让我们探索一个简单的计算器示例：

```
┌─────────────────────────────────────────────────────────────────────┐
│ 有状态计算器                                                        │
│                                                                     │
│ 系统："你是一个维护运行总数的计算器助手。逐步跟随用户的数学操作。" │
│                                                                     │
│ 状态：{ "当前值": 0 }                                               │
│                                                                     │
│ 用户："从5开始"                                                     │
│ 助手："从5开始。当前值是5。"                                        │
│ 状态：{ "当前值": 5 }                                               │
│                                                                     │
│ 用户："乘以3"                                                       │
│ 助手："5 × 3 = 15。当前值是15。"                                    │
│ 状态：{ "当前值": 15 }                                              │
│                                                                     │
│ 用户："加7"                                                         │
│ 助手："15 + 7 = 22。当前值是22。"                                   │
│ 状态：{ "当前值": 22 }                                              │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

状态变量在轮次间持续存在，实现连续计算。

## 长期记忆：超越上下文窗口

对于真正持久的记忆，我们需要外部存储：

```
┌──────────────────────────────────────────────────────────────────────────┐
│                                                                          │
│   用户输入                                                               │
│       │                                                                  │
│       ▼                                                                  │
│  ┌─────────────┐                                                         │
│  │ 提取        │                                                         │
│  │ 关键信息    │                                                         │
│  └─────────────┘                                                         │
│       │                                                                  │
│       ▼                                                                  │
│  ┌─────────────┐      ┌────────────────────┐                             │
│  │ 更新        │◄─────┤ 外部记忆           │                             │
│  │ 记忆        │      │ (向量数据库,       │                             │
│  │             │─────►│  文档数据库等)     │                             │
│  └─────────────┘      └────────────────────┘                             │
│       │                        ▲                                         │
│       │                        │                                         │
│       ▼                        │                                         │
│  ┌─────────────┐      ┌────────────────────┐                             │
│  │ 构建        │      │ 检索相关           │                             │
│  │ 上下文      │◄─────┤ 记忆               │                             │
│  │             │      │                    │                             │
│  └─────────────┘      └────────────────────┘                             │
│       │                                                                  │
│       ▼                                                                  │
│  ┌─────────────┐                                                         │
│  │             │                                                         │
│  │ LLM         │                                                         │
│  │             │                                                         │
│  └─────────────┘                                                         │
│       │                                                                  │
│       ▼                                                                  │
│   响应                                                                   │
│                                                                          │
└──────────────────────────────────────────────────────────────────────────┘
```

这种架构通过以下方式实现潜在无限的记忆：
1. 从对话中提取关键信息
2. 将其存储在外部数据库中
3. 在需要时检索相关上下文
4. 将该上下文纳入提示中

## 细胞实现：记忆管理器

这是一个实现基本记忆管理的Python类：

```python
class ContextCell:
    """维护跨交互记忆的上下文细胞。"""

    def __init__(self, system_prompt, max_turns=10, memory_strategy="window"):
        """
        初始化上下文细胞。

        Args:
            system_prompt (str): 系统指令
            max_turns (int): 保留的最大对话轮次
            memory_strategy (str): 'window', 'summarize', 或 'key_value'
        """
        self.system_prompt = system_prompt
        self.max_turns = max_turns
        self.memory_strategy = memory_strategy
        self.conversation_history = []
        self.key_value_store = {}

    def add_exchange(self, user_input, assistant_response):
        """将对话交换添加到历史中。"""
        self.conversation_history.append({
            "user": user_input,
            "assistant": assistant_response
        })

        # 如果需要，应用记忆管理
        if len(self.conversation_history) > self.max_turns:
            self._manage_memory()

    def extract_info(self, key, value):
        """在键值存储中存储重要信息。"""
        self.key_value_store[key] = value

    def _manage_memory(self):
        """应用选定的记忆管理策略。"""
        if self.memory_strategy == "window":
            # 只保留最近的轮次
            self.conversation_history = self.conversation_history[-self.max_turns:]

        elif self.memory_strategy == "summarize":
            # 摘要较旧的轮次（实际中会使用LLM）
            to_summarize = self.conversation_history[:-self.max_turns + 1]
            summary = self._create_summary(to_summarize)

            # 用摘要替换旧轮次
            self.conversation_history = [{"summary": summary}] + \
                                       self.conversation_history[-(self.max_turns-1):]

    def _create_summary(self, exchanges):
        """创建对话交换的摘要。"""
        # 实际中，这会调用LLM来创建摘要
        # 对于这个示例，我们使用占位符
        return f"{len(exchanges)}个之前交换的摘要"

    def build_context(self, current_input):
        """为下一次LLM调用构建完整上下文。"""
        context = f"{self.system_prompt}\n\n"

        # 如果有键值记忆，添加它
        if self.key_value_store:
            context += "记忆：\n"
            for key, value in self.key_value_store.items():
                context += f"{key}: {value}\n"
            context += "\n"

        # 添加对话历史
        if self.conversation_history:
            context += "对话历史：\n"
            for exchange in self.conversation_history:
                if "summary" in exchange:
                    context += f"[之前的交换：{exchange['summary']}]\n\n"
                else:
                    context += f"用户：{exchange['user']}\n"
                    context += f"助手：{exchange['assistant']}\n\n"

        # 添加当前输入
        context += f"用户：{current_input}\n助手："

        return context
```

## 测量细胞效率

与分子一样，测量效率对细胞至关重要：

```
┌─────────────────────────────────────────────────────────────────┐
│ 记忆策略比较                                                    │
├──────────────────┬──────────────┬─────────────┬─────────────────┤
│ 策略             │ 令牌使用     │ 信息        │ 实现            │
│                  │              │ 保留        │ 复杂度          │
├──────────────────┼──────────────┼─────────────┼─────────────────┤
│ 无记忆           │ 最低         │ 无          │ 简单            │
├──────────────────┼──────────────┼─────────────┼─────────────────┤
│ 完整历史         │ 最高         │ 完整        │ 简单            │
├──────────────────┼──────────────┼─────────────┼─────────────────┤
│ 窗口化           │ 可控         │ 仅最近      │ 容易            │
├──────────────────┼──────────────┼─────────────┼─────────────────┤
│ 摘要化           │ 中等         │ 良好        │ 中等            │
├──────────────────┼──────────────┼─────────────┼─────────────────┤
│ 键值存储         │ 低           │ 选择性      │ 中等            │
├──────────────────┼──────────────┼─────────────┼─────────────────┤
│ 外部存储         │ 很低         │ 广泛        │ 复杂            │
└──────────────────┴──────────────┴─────────────┴─────────────────┘
```

不同策略针对不同优先级进行优化。选择正确的方法取决于你的具体应用需求。

## 高级技术：记忆编排

对于复杂的应用，多个记忆系统可以协同工作：

```
┌─────────────────────────────────────────────────────────────────────┐
│                      记忆编排                                       │
│                                                                     │
│  ┌─────────────────┐    ┌─────────────────┐   ┌─────────────────┐   │
│  │                 │    │                 │   │                 │   │
│  │ 短期            │    │ 工作            │   │ 长期            │   │
│  │ 记忆            │    │ 记忆            │   │ 记忆            │   │
│  │                 │    │                 │   │                 │   │
│  │ • 最近轮次      │    │ • 当前任务      │   │ • 用户档案      │   │
│  │ • 即时          │    │ • 活跃          │   │ • 历史          │   │
│  │   上下文        │    │   变量          │   │   事实          │   │
│  │ • 最后几次      │    │ • 任务进度      │   │ • 学习到的      │   │
│  │   交换          │    │ • 任务中        │   │   偏好          │   │
│  │                 │    │   状态          │   │                 │   │
│  └─────────────────┘    └─────────────────┘   └─────────────────┘   │
│         ▲ ▼                   ▲ ▼                   ▲ ▼             │
│         │ │                   │ │                   │ │             │
│  ┌──────┘ └───────────────────┘ └───────────────────┘ └──────┐      │
│  │                                                           │      │
│  │                    记忆管理器                             │      │
│  │                                                           │      │
│  └───────────────────────────────┬───────────────────────────┘      │
│                                  │                                  │
│                                  ▼                                  │
│                        ┌─────────────────┐                          │
│                        │                 │                          │
│                        │   上下文        │                          │
│                        │   构建器        │                          │
│                        │                 │                          │
│                        └─────────────────┘                          │
│                                  │                                  │
│                                  ▼                                  │
│                        ┌─────────────────┐                          │
│                        │                 │                          │
│                        │      LLM        │                          │
│                        │                 │                          │
│                        └─────────────────┘                          │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

这种架构镜像人类记忆系统，具有：
- **短期记忆**：最近的对话轮次
- **工作记忆**：活跃的任务状态和变量
- **长期记忆**：持久的用户信息和偏好

记忆管理器编排这些系统，决定在每个上下文中包含哪些信息。

## 记忆和幻觉减少

记忆细胞最有价值的好处之一是减少幻觉：

```
┌─────────────────────────────────────────────────────────────────────┐
│ 幻觉减少策略                                                        │
├─────────────────────────────────────────────────────────────────────┤
│ 1. 明确存储从之前交换中提取的事实                                   │
│ 2. 用来源/确定性级别标记信息                                        │
│ 3. 当类似话题出现时在上下文中包含相关事实                           │
│ 4. 检测并纠正记忆和响应之间的矛盾                                   │
│ 5. 定期通过用户确认验证重要事实                                     │
└─────────────────────────────────────────────────────────────────────┘
```

通过将LLM建立在来自记忆的一致事实基础上，我们显著提高了可靠性。

## 超越文本：结构化状态

高级细胞维护超越文本历史的结构化状态：

```
┌─────────────────────────────────────────────────────────────────────┐
│ 结构化状态示例                                                      │
├─────────────────────────┬───────────────────────────────────────────┤
│ 进度状态                │ {"步骤": 3, "已完成步骤": [1, 2],         │
│                         │  "下一动作": "验证输入"}                  │
├─────────────────────────┼───────────────────────────────────────────┤
│ 用户档案                │ {"姓名": "Alex", "偏好": {                │
│                         │  "沟通风格": "简洁",                      │
│                         │  "专业水平": "初学者"}}                   │
├─────────────────────────┼───────────────────────────────────────────┤
│ 应用状态                │ {"当前视图": "仪表板",                    │
│                         │  "过滤器": ["活跃", "高优先级"],          │
│                         │  "排序方式": "截止日期"}                  │
├─────────────────────────┼───────────────────────────────────────────┤
│ 环境上下文              │ {"位置": "多伦多",                        │
│                         │  "天气": "下雪",                          │
│                         │  "时间": "晚上"}                          │
└─────────────────────────┴───────────────────────────────────────────┘
```

这种结构化方法允许精确控制上下文并实现更复杂的应用。

## 记忆反馈循环

复杂的细胞创建反馈循环，其中LLM帮助管理自己的记忆：

```
┌─────────────────────────────────────────────────────────────────────┐
│                                                                     │
│  用户："我计划下个月去日本旅行。"                                   │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────────┐│
│  │ [内部记忆提取]                                                  ││
│  │ 要记住的重要事实：                                              ││
│  │ - 用户计划去日本旅行                                            ││
│  │ - 旅行安排在下个月                                              ││
│  │ 置信度：高                                                      ││
│  └─────────────────────────────────────────────────────────────────┘│
│                                                                     │
│  助手："太令人兴奋了！日本很美。你对东京和京都这样的城市感兴趣，   │
│  还是更偏向乡村地区？"                                              │
│                                                                     │
│  用户："绝对是东京，也许还有大阪。"                                 │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────────┐│
│  │ [内部记忆更新]                                                  ││
│  │ 更新的事实：                                                    ││
│  │ - 用户计划下个月去日本旅行                                      ││
│  │ - 用户对东京和大阪感兴趣                                        ││
│  │ - 用户可能对乡村地区不感兴趣（置信度：中等）                    ││
│  └─────────────────────────────────────────────────────────────────┘│
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

LLM本身提取和更新要记住的重要信息，创建一个自我改进的记忆系统。

## 关键要点

1. **记忆细胞**在多次交互中添加状态持久性
2. **令牌预算管理**在对话增长时至关重要
3. **记忆策略**包括窗口化、摘要化和键值存储
4. **外部记忆**实现超越上下文窗口的无限、持久存储
5. **结构化状态**实现超越简单对话的复杂应用
6. **记忆编排**结合多个记忆系统以获得最佳性能
7. **自我改进记忆**使用LLM帮助管理自己的记忆

## 练习实践

1. 实现一个带窗口化的简单对话记忆系统
2. 在同一扩展对话上比较不同的记忆策略
3. 构建一个从对话中提取重要事实的键值存储
4. 实验使用LLM来摘要较旧的对话轮次
5. 为特定应用领域创建结构化状态管理器

## 下一步

在下一节中，我们将探索**器官** — 多智能体系统，其中多个上下文细胞协同工作解决复杂问题。

[继续到 04_organs_applications.md →](04_organs_applications.md)

---

## 深入探讨：记忆抽象

记忆可以在多个抽象层次中组织：

```
┌────────────────────────────────────────────────────────────────────┐
│ 记忆抽象层次                                                       │
├────────────────────────────────────────────────────────────────────┤
│                                                                    │
│   ┌─────────────────┐                                              │
│   │ 情节记忆        │  特定的对话交换和事件                        │
│   └─────────────────┘                                              │
│           ▲                                                        │
│           │                                                        │
│   ┌─────────────────┐                                              │
│   │ 语义记忆        │  事实、概念和结构化知识                      │
│   └─────────────────┘                                              │
│           ▲                                                        │
│           │                                                        │
│   ┌─────────────────┐                                              │
│   │ 概念            │  高级模式、偏好、目标                        │
│   │ 记忆            │                                              │
│   └─────────────────┘                                              │
│                                                                    │
└────────────────────────────────────────────────────────────────────┘
```

这种分层方法允许系统平衡具体细节与对交互上下文的高级理解。
