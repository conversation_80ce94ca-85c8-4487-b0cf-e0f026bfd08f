# 原子：提示工程的基本单元

> "如果你想从头开始制作苹果派，你必须先发明宇宙。" — 卡尔·萨根

## 原子：单一指令

在我们的上下文工程之旅中，我们从最基本的单元开始：**原子** — 对LLM的单一、独立指令。

```
┌───────────────────────────────────────────────┐
│                                               │
│  "写一首关于海洋的4行诗。"                      │
│                                               │
└───────────────────────────────────────────────┘
```

这是最纯粹形式的提示工程：一个人，一个指令，一个模型响应。简单、直接、原子化。

## 原子提示的解剖结构

让我们分解一个有效原子提示的组成要素：

```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│  原子提示 = [任务] + [约束条件] + [输出格式]                  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

例如：

```
┌─────────────────────┬────────────────────────┬────────────────────┐
│        任务         │       约束条件         │     输出格式       │
├─────────────────────┼────────────────────────┼────────────────────┤
│ "写一首关于太空的诗" │ "关于海洋，只使用      │ "4行。"            │
│                     │  5个字母或更少的       │                    │
│                     │  单词。"               │                    │
└─────────────────────┴────────────────────────┴────────────────────┘
```

## 原子的局限性

虽然原子提示是LLM交互的构建块，但它们很快就暴露出根本性的局限：

```
┌──────────────────────────────────────┐
│ 原子提示的局限性                     │
├──────────────────────────────────────┤
│ ✗ 交互间无记忆                       │
│ ✗ 有限的演示能力                     │
│ ✗ 无复杂推理脚手架                   │
│ ✗ 容易产生歧义                       │
│ ✗ 输出变异性高                       │
└──────────────────────────────────────┘
```

让我们通过一个简单的实验来实证测量这一点：

```python
# 基本原子提示
atomic_prompt = "列出糖尿病的5个症状。"

# 多次发送给LLM
responses = [llm.generate(atomic_prompt) for _ in range(5)]

# 测量变异性
unique_symptoms = set()
for response in responses:
    symptoms = extract_symptoms(response)
    unique_symptoms.update(symptoms)

print(f"在5个相同提示中发现了{len(unique_symptoms)}个独特症状")
# 通常输出远超过5个独特症状
```

问题在哪？模型在给定最少上下文时难以保持一致性。

## 单原子基线：有用但有限

尽管有局限性，原子提示建立了我们的基线。它们帮助我们：

1. 测量令牌效率（最小开销）
2. 基准响应质量
3. 为实验建立对照组

```
                     [响应质量]
                            ▲
                            │
                            │               ⭐ 上下文
                            │                 工程
                            │               
                            │           
                            │       ⭐ 高级
                            │         提示
                            │
                            │   ⭐ 基础提示
                            │
                            │
                            └────────────────────────►
                                  [复杂度]
```

## 未言明的上下文：模型已经"知道"的

即使使用原子提示，LLM也会利用来自训练的大量隐式上下文：

```
┌───────────────────────────────────────────────────────────────┐
│ 模型中的隐式上下文                                            │
├───────────────────────────────────────────────────────────────┤
│ ✓ 语言规则和语法                                              │
│ ✓ 常识事实                                                    │
│ ✓ 格式约定（列表、段落等）                                    │
│ ✓ 领域特定知识（因模型而异）                                  │
│ ✓ 学习到的交互模式                                            │
└───────────────────────────────────────────────────────────────┘
```

这种隐式知识为我们提供了基础，但它不可靠且在不同模型和版本间存在差异。

## 幂律：令牌-质量曲线

对于许多任务，我们观察到上下文令牌和输出质量之间的幂律关系：

```
质量
      ▲
      │                        •
      │                    •       •
      │                •               •
      │            •                       •
      │        •                               •
      │    •
      │•
      └───────────────────────────────────────────► 令牌
          [糟糕开始]  [最大ROI]  [收益递减]
```

关键洞察：存在一个"最大ROI区域"，在这里添加少量令牌就能产生显著的质量改进，以及"收益递减"区域，在这里添加更多令牌反而会降低性能。

## [阅读更多关于上下文腐烂](https://research.trychroma.com/context-rot)

## 从原子到分子：对更多上下文的需求

原子的局限性自然地引导我们到下一步：**分子**，或者说将指令与示例、附加上下文和结构化格式相结合的多部分提示。

这是基本的转换：

```
┌──────────────────────────┐         ┌──────────────────────────┐
│                          │         │ "这里是一个例子：        │
│ "写一首关于程序员的      │    →    │  从前有一个...           │
│  打油诗。"               │         │                          │
│                          │         │  现在写一首关于程序员的  │
└──────────────────────────┘         │  打油诗。"               │
                                     └──────────────────────────┘
    [原子提示]                           [分子提示]
```

通过添加示例和结构，我们开始有意识地塑造上下文窗口——这是迈向上下文工程的第一步。

## 测量原子效率：你的第一个任务

在继续之前，尝试这个简单的练习：

1. 选择一个你会给LLM的基本任务
2. 创建三个不同的原子提示版本
3. 测量使用的令牌和主观质量
4. 绘制效率前沿

```
┌─────────────────────────────────────────────────────────────┐
│ 任务：总结新闻文章                                          │
├─────────┬───────────────────────────────┬────────┬──────────┤
│ 版本    │ 提示                          │ 令牌   │ 质量     │
├─────────┼───────────────────────────────┼────────┼──────────┤
│ A       │ "总结这篇文章。"              │ 4      │ 2/10     │
├─────────┼───────────────────────────────┼────────┼──────────┤
│ B       │ "用3句话提供这篇文章的        │ 14     │ 6/10     │
│         │  简洁摘要。"                  │        │          │
├─────────┼───────────────────────────────┼────────┼──────────┤
│ C       │ "写一份这篇文章要点的摘要，   │ 27     │ 8/10     │
│         │  突出主要人物和事件。"        │        │          │
└─────────┴───────────────────────────────┴────────┴──────────┘
```

## 关键要点

1. **原子提示**是LLM交互的基本单元
2. 它们遵循基本结构：任务 + 约束条件 + 输出格式
3. 它们有固有局限性：无记忆、示例或推理脚手架
4. 即使简单的原子提示也利用了模型的隐式知识
5. 上下文令牌和质量之间存在幂律关系
6. 超越原子是迈向上下文工程的第一步

## 下一步

在下一节中，我们将探索如何将原子组合成**分子** — 显著提高可靠性和控制力的少样本学习模式。

[继续到 02_molecules_context.md →](02_molecules_context.md)

---

## 深入探讨：提示模板

对于那些想要更多地实验原子提示的人，这里有一些模板可以尝试：

```
# 基本指令
{任务}

# 基于角色
作为一个{角色}，{任务}

# 特定格式
{任务}
格式：{格式规范}

# 基于约束
{任务}
约束条件：
- {约束条件_1}
- {约束条件_2}
- {约束条件_3}

# 逐步指导
{任务}
请遵循以下步骤：
1. {步骤_1}
2. {步骤_2}
3. {步骤_3}
```

尝试测量每个模板应用于同一任务时的令牌数量和质量！
