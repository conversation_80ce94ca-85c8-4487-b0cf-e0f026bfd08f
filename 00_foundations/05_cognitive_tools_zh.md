# 认知工具：扩展上下文工程框架

> "心灵不是一个需要被填满的容器，而是一团需要被点燃的火焰。" — 普鲁塔克

## 从生物学到认知

我们的上下文工程之旅遵循了生物学隐喻：

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │     │          │
│  原子    │────►│  分子    │────►│  细胞    │────►│  器官    │
│          │     │          │     │          │     │          │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
    │                │                │                │
    ▼                ▼                ▼                ▼
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │     │          │
│  提示    │     │ 少样本   │     │  记忆    │     │  多智能  │
│          │     │          │     │          │     │  体系统  │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
```

现在，我们将通过与人类认知的类比来扩展这个框架。就像人类思维使用认知工具来高效处理信息一样，我们可以为大语言模型创建类似的结构：

```
┌─────────────────────────────────────────────────────────────────────┐
│                      认知工具扩展                                    │
├──────────┬───────────────────┬──────────────────────────────────────┤
│          │                   │                                      │
│ 人类     │ 启发式方法        │ 简化复杂问题的心理捷径               │
│ 认知     │                   │                                      │
│          │                   │                                      │
├──────────┼───────────────────┼──────────────────────────────────────┤
│          │                   │                                      │
│ 大语言   │ 提示程序          │ 指导模型推理的结构化提示模式         │
│ 模型     │                   │                                      │
│ 对应     │                   │                                      │
└──────────┴───────────────────┴──────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│                                                                     │
├──────────┬───────────────────┬──────────────────────────────────────┤
│          │                   │                                      │
│ 人类     │ 图式              │ 帮助分类信息的有组织的知识结构       │
│ 认知     │                   │                                      │
│          │                   │                                      │
├──────────┼───────────────────┼──────────────────────────────────────┤
│          │                   │                                      │
│ 大语言   │ 上下文图式        │ 为模型结构化信息的标准化格式         │
│ 模型     │                   │                                      │
│ 对应     │                   │                                      │
└──────────┴───────────────────┴──────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│                                                                     │
├──────────┬───────────────────┬──────────────────────────────────────┤
│          │                   │                                      │
│ 人类     │ 启动效应          │ 激活某些关联，影响后续思考           │
│ 认知     │                   │                                      │
│          │                   │                                      │
├──────────┼───────────────────┼──────────────────────────────────────┤
│          │                   │                                      │
│ 大语言   │ 递归              │ 塑造模型行为模式的自指提示           │
│ 模型     │ 提示              │                                      │
│ 对应     │                   │                                      │
└──────────┴───────────────────┴──────────────────────────────────────┘
```


## 认知工具？ 

### **[在语言模型中使用认知工具引发推理 - IBM苏黎世 2025年6月](https://www.arxiv.org/pdf/2506.12115)**

### 提示和提示程序作为推理工具调用
> "认知工具"将推理操作封装在大语言模型内部 — [IBM苏黎世](https://www.arxiv.org/pdf/2506.12115)



![image](https://github.com/user-attachments/assets/cd06c3f5-5a0b-4ee7-bbba-2f9f243f70ae)

> **这些认知工具（作为工具调用的结构化提示模板）通过识别手头的主要概念、提取问题中的相关信息，以及突出可能有助于解决问题的有意义的属性、定理和技术来分解问题。**

![image](https://github.com/user-attachments/assets/f7ce8605-6fa3-494f-94cd-94e6b23032b6)


> **这些模板搭建了类似于认知心理捷径的推理层，通常被研究为"启发式方法"。**

## 提示程序：大语言模型的算法思维（推理工具调用）

提示程序是一种结构化、可重用的提示模式，旨在指导大语言模型的推理过程——类似于启发式方法指导人类思维。

### 从临时提示到程序化模式

让我们比较一个临时提示和一个简单的提示程序（推理工具调用）：

```
┌───────────────────────────────────────────────────────────────┐
│ 临时提示                                                      │
├───────────────────────────────────────────────────────────────┤
│ "用3段话总结这篇关于气候变化的文章。让它易于理解。"           │
└───────────────────────────────────────────────────────────────┘

┌───────────────────────────────────────────────────────────────┐
│ 提示程序                                                      │
├───────────────────────────────────────────────────────────────┤
│ program Summarize(text, paragraphs=3, complexity="simple") {  │
│   // 定义任务                                                 │
│   task = `用${paragraphs}段话总结以下文本。                   │
│           使用${complexity}语言。`;                           │
│                                                               │
│   // 定义过程                                                 │
│   process = ```                                               │
│     1. 识别主要话题和关键点                                   │
│     2. 按重要性组织要点                                       │
│     3. 创建连贯的摘要，包含：                                 │
│        - 第一段：主要话题和背景                               │
│        - 中间段落：关键支持细节                               │
│        - 最后一段：结论或影响                                 │
│   ```;                                                        │
│                                                               │
│   // 定义输出格式                                             │
│   format = "使用${complexity}语言的${paragraphs}段摘要。";    │
│                                                               │
│   // 构建完整提示                                             │
│   return `${task}\n\n过程:\n${process}\n\n                   │
│           格式:\n${format}\n\n要总结的文本:\n${text}`;        │
│ }                                                             │
└───────────────────────────────────────────────────────────────┘
```

提示程序方法提供了几个优势：
1. **可重用性**：同样的模式可以应用于不同的文本
2. **参数化**：轻松自定义长度、复杂性等
3. **透明性**：清晰的结构使提示的意图明确
4. **一致性**：在多次运行中产生更可预测的结果

### 简单提示程序模板

这是创建自己的提示程序的基本模板：

```
program [名称]([参数]) {
  // 定义任务
  task = `[使用参数的清晰指令]`;
  
  // 定义过程
  process = ```
    1. [第一步]
    2. [第二步]
    3. [根据需要添加更多步骤]
  ```;
  
  // 定义输出格式
  format = "[期望的响应结构]";
  
  // 构建完整提示
  return `${task}\n\n过程:\n${process}\n\n格式:\n${format}\n\n[输入]`;
}
```

在实践中，这个模板可以通过各种方式实现：
- 作为文档中的伪代码或协议外壳
- 作为生成提示的实际JavaScript/Python函数
- 作为带有变量替换的YAML模板
- 作为用于标准化提示构建的JSON模式

## 推理提示模板（工具调用）

### 1. 逐步推理

将复杂推理分解为可管理步骤的基本模板。

```markdown
# 逐步推理模板

任务：通过将以下问题分解为清晰、逻辑的步骤来解决。

问题：{{problem}}

请遵循以下过程：
1. **理解**：重新陈述问题并确定你需要找到什么。
2. **计划**：概述解决问题的方法。
3. **执行**：详细完成计划的每一步。
   - 步骤1：[第一步的描述]
   - 步骤2：[第二步的描述]
   - 步骤3：[根据需要继续添加步骤]
4. **验证**：根据原始问题检查你的解决方案。
5. **结论**：清楚地陈述你的最终答案或结论。

展示你的所有工作并解释每一步的推理。
```

**令牌数**：约130个令牌（仅模板）

## 什么是协议外壳？（推理工具调用）

协议外壳是结构化的无代码模板，将与AI系统的通信组织成清晰、一致的模式。将它们视为建立以下内容的对话蓝图：

1. **意图**：你试图完成什么
2. **输入**：你提供什么信息
3. **过程**：信息应该如何处理
4. **输出**：你期望什么结果

### 基本协议外壳结构

```
/protocol.name{
    intent="目的的清晰陈述",
    input={
        param1="value1",
        param2="value2"
    },
    process=[
        /step1{action="做某事"},
        /step2{action="做其他事"}
    ],
    output={
        result1="期望输出1",
        result2="期望输出2"
    }
}
```

这种结构创建了一个清晰、令牌高效的框架，你和AI都可以遵循。

**反思练习**：查看你最近的AI对话。你能识别出你一直在使用的隐式结构吗（即情感背景、潜在意图、长期目标、矛盾输入等）？将这些形式化为协议外壳并使数据更明确如何改善你的交互？

## 协议外壳的解剖

让我们解剖协议外壳的每个组件，以了解其目的和力量：

```
┌─────────────────────────────────────────────────────────┐
│                    协议解剖                             │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  /protocol.name{                                        │
│    │       │                                            │
│    │       └── 子类型或特定变体                         │
│    │                                                    │
│    └── 核心协议类型                                     │
│                                                         │
│    intent="目的的清晰陈述",                             │
│    │       │                                            │
│    │       └── 指导AI理解目标                           │
│    │                                                    │
│    └── 声明目标                                         │
│                                                         │
│    input={                                              │
│        param1="value1",   ◄── 结构化输入数据           │
│        param2="value2"                                  │
│    },                                                   │
│                                                         │
│    process=[                                            │
│        /step1{action="做某事"},     ◄── 有序             │
│        /step2{action="做其他事"} ◄── 步骤               │
│    ],                                                   │
│                                                         │
│    output={                                             │
│        result1="期望输出1",   ◄── 输出                  │
│        result2="期望输出2"    ◄── 规范                  │
│    }                                                    │
│  }                                                      │
│                                                         │
└─────────────────────────────────────────────────────────┘
```


## 上下文图式：结构化信息模式


就像人类思维使用图式来组织知识一样，我们可以为大语言模型创建上下文图式——结构化信息以改善模型理解的标准化方式。

### 基本图式结构

```
┌───────────────────────────────────────────────────────────────┐
│ 上下文图式                                                    │
├───────────────────────────────────────────────────────────────┤
│ {                                                             │
│   "$schema": "context-engineering/schemas/v1.json",           │
│   "title": "分析请求图式",                                    │
│   "description": "请求分析的标准格式",                        │
│   "type": "object",                                           │
│   "properties": {                                             │
│     "task": {                                                 │
│       "type": "string",                                       │
│       "description": "要执行的分析任务"                       │
│     },                                                        │
│     "context": {                                              │
│       "type": "object",                                       │
│       "properties": {                                         │
│         "background": { "type": "string" },                   │
│         "constraints": { "type": "array" },                   │
│         "examples": { "type": "array" }                       │
│       }                                                       │
│     },                                                        │
│     "data": {                                                 │
│       "type": "string",                                       │
│       "description": "要分析的信息"                           │
│     },                                                        │
│     "output_format": {                                        │
│       "type": "string",                                       │
│       "enum": ["bullet_points", "paragraphs", "table"]        │
│     }                                                         │
│   },                                                          │
│   "required": ["task", "data"]                                │
│ }                                                             │
└───────────────────────────────────────────────────────────────┘
```


### **[MEM1：学习协同记忆和推理以实现高效长期智能体 - 新加坡-MIT 2025年6月](https://www.arxiv.org/pdf/2506.12115)**

> "我们的结果证明了推理驱动的记忆整合作为训练长期交互智能体的可扩展替代方案的前景，其中效率和性能都得到了优化。" — [新加坡-MIT](https://arxiv.org/pdf/2506.15841)

![image](https://github.com/user-attachments/assets/16e3f241-5f44-4ed5-9622-f0b4acbb67b0)


### 从图式到提示

图式可以通过填充结构化模板转换为实际提示：

```
# 分析请求

## 任务
识别提供文本中的主要主题和支持证据。

## 上下文
### 背景
这是2023年在气候会议上发表的演讲。

### 约束
- 专注于科学声明
- 忽略政治声明
- 保持中立

### 示例
- 主题：海平面上升
  证据："测量显示自2010年以来每年上升3.4毫米"

## 数据
[演讲的完整文本将放在这里]

## 输出格式
bullet_points
```

这种结构化方法帮助模型准确理解提供了什么信息以及期望什么回报。

## 递归提示：自指改进

递归提示类似于认知启动——它建立影响后续模型行为的模式。关键洞察是让模型反思并改进自己的输出。

### 基本递归模式

```
┌───────────────────────────────────────────────────────────────┐
│ 递归提示流程                                                  │
│                                                               │
│  ┌─────────────┐      ┌─────────────┐      ┌─────────────┐    │
│  │             │      │             │      │             │    │
│  │  初始       │─────►│  自我       │─────►│  改进的     │    │
│  │  响应       │      │  反思       │      │  响应       │    │
│  │             │      │             │      │             │    │
│  └─────────────┘      └─────────────┘      └─────────────┘    │
│        ▲                                          │           │
│        └──────────────────────────────────────────┘           │
│                                                               │
└───────────────────────────────────────────────────────────────┘
```

### 简单实现

```python
def recursive_prompt(question, model, iterations=2):
    """应用递归提示来改进响应。"""
    
    # 初始响应
    response = model.generate(f"问题：{question}\n答案：")
    
    for i in range(iterations):
        # 自我反思提示
        reflection_prompt = f"""
        问题：{question}
        
        你之前的答案：
        {response}
        
        请反思你的答案：
        1. 可能缺少什么信息？
        2. 有什么假设应该被质疑？
        3. 如何使解释更清晰或更准确？
        
        现在，提供一个改进的答案：
        """
        
        # 生成改进的响应
        response = model.generate(reflection_prompt)
    
    return response
```

这种简单的递归模式可以通过鼓励模型批评和完善自己的思维来显著提高响应质量。

## 整合：认知架构

这些认知工具可以组合成一个完整的架构，镜像人类思维过程：

```
┌───────────────────────────────────────────────────────────────────────────┐
│                      认知架构                                             │
│                                                                           │
│  ┌─────────────────┐                                                      │
│  │                 │                                                      │
│  │  输入解析器     │  使用图式识别理解用户意图                            │
│  │                 │                                                      │
│  └─────────────────┘                                                      │
│         │                                                                 │
│         ▼                                                                 │
│  ┌─────────────────┐                                                      │
│  │                 │                                                      │
│  │  提示程序       │  选择并应用适当的推理模式                            │
│  │  选择器         │                                                      │
│  │                 │                                                      │
│  └─────────────────┘                                                      │
│         │                                                                 │
│         ▼                                                                 │
│  ┌─────────────────┐                                                      │
│  │                 │                                                      │
│  │  工作记忆       │  在步骤间维护状态和上下文                            │
│  │                 │                                                      │
│  └─────────────────┘                                                      │
│         │                                                                 │
│         ▼                                                                 │
│  ┌─────────────────┐                                                      │
│  │                 │                                                      │
│  │  递归           │  通过反思应用自我改进                                │
│  │  处理器         │                                                      │
│  │                 │                                                      │
│  └─────────────────┘                                                      │
│         │                                                                 │
│         ▼                                                                 │
│  ┌─────────────────┐                                                      │
│  │                 │                                                      │
│  │  输出           │  根据图式格式化最终响应                              │
│  │  格式化器       │                                                      │
│  │                 │                                                      │
│  └─────────────────┘                                                      │
│                                                                           │
└───────────────────────────────────────────────────────────────────────────┘
```

这种架构可以使用我们讨论的工具和模式实现为完整系统。

## 关键要点

1. **提示程序/协议**像人类启发式方法一样结构化推理
2. **上下文图式**像心理知识结构一样组织信息
3. **递归提示**创建类似认知反思的自我改进循环
4. **认知架构**将这些工具组合成完整系统

这些对我们上下文工程框架的认知扩展允许我们创建更复杂但可理解的大语言模型工作方法。

## 实践练习

1. 将你经常使用的提示之一转换为提示程序
2. 为你使用大语言模型执行的常见任务创建简单图式
3. 实现基本递归提示以提高响应质量
4. 将这些方法组合成迷你认知架构

## 下一步

在接下来的部分中，我们将探索这些认知工具的实际实现：
- 演示提示程序实际应用的Jupyter笔记本
- 创建自己图式的模板
- 完整认知架构的示例

[继续到下一节 →](06_advanced_applications.md)

---

## 深入探讨：从我们的研究到你的应用

上述描述的认知工具是更高级研究概念的简化表示。对于有兴趣进一步探索的人：

- **提示程序**是研究人员称为"程序化提示"或"结构化提示框架"的实际实现
- **上下文图式**代表知识表示系统和本体框架的简化版本
- **递归提示**与AI系统中的自我反思、元认知和递归自我改进相关

这些简化框架使高级概念变得可访问，同时保持其实用性。
