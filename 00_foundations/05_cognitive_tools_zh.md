# 认知工具：扩展上下文工程框架

> "心智不是要被填满的容器，而是要被点燃的火焰。" — 普鲁塔克

## 从生物学到认知

我们的上下文工程之旅遵循了生物学隐喻：

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │     │          │
│  原子    │────►│  分子    │────►│  细胞    │────►│  器官    │
│          │     │          │     │          │     │          │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
    │                │                │                │
    ▼                ▼                ▼                ▼
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │     │          │
│  提示    │     │ 少样本   │     │  记忆    │     │  多智    │
│          │     │          │     │          │     │  能体    │
└──────────┘     └──────────┘     └──────────┘     └──────────┘
```

现在，我们将通过与人类认知的相似性来扩展这个框架。就像人类思维使用认知工具来高效处理信息一样，我们可以为LLM创建类似的结构：

```
┌─────────────────────────────────────────────────────────────────────┐
│                      认知工具扩展                                   │
├──────────┬───────────────────┬──────────────────────────────────────┤
│          │                   │                                      │
│ 人类     │ 启发式            │ 简化复杂问题的心理捷径               │
│ 认知     │                   │                                      │
│          │                   │                                      │
├──────────┼───────────────────┼──────────────────────────────────────┤
│          │                   │                                      │
│ LLM      │ 提示程序          │ 指导模型推理的结构化提示模式         │
│ 对应     │                   │                                      │
│          │                   │                                      │
└──────────┴───────────────────┴──────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│                                                                     │
├──────────┬───────────────────┬──────────────────────────────────────┤
│          │                   │                                      │
│ 人类     │ 图式              │ 帮助分类信息的有组织的知识结构       │
│ 认知     │                   │                                      │
│          │                   │                                      │
├──────────┼───────────────────┼──────────────────────────────────────┤
│          │                   │                                      │
│ LLM      │ 上下文图式        │ 组织信息处理的标准化格式             │
│ 对应     │                   │                                      │
│          │                   │                                      │
└──────────┴───────────────────┴──────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│                                                                     │
├──────────┬───────────────────┬──────────────────────────────────────┤
│          │                   │                                      │
│ 人类     │ 元认知            │ 对思维过程的思考                     │
│ 认知     │                   │                                      │
│          │                   │                                      │
├──────────┼───────────────────┼──────────────────────────────────────┤
│          │                   │                                      │
│ LLM      │ 元提示            │ 指导模型如何思考和推理的提示         │
│ 对应     │                   │                                      │
│          │                   │                                      │
└──────────┴───────────────────┴──────────────────────────────────────┘
```

## 认知工具1：启发式和提示程序

启发式是人类用来快速做出决策的心理捷径。在LLM中，我们可以创建"提示程序" — 编码特定推理模式的结构化提示。

### 问题解决启发式

```
┌─────────────────────────────────────────────────────────────────────┐
│ 问题解决提示程序                                                    │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│ 1. 问题定义：                                                       │
│    - 问题是什么？                                                   │
│    - 已知什么？                                                     │
│    - 需要找到什么？                                                 │
│                                                                     │
│ 2. 策略选择：                                                       │
│    - 这类似于我以前见过的问题吗？                                   │
│    - 我可以将其分解为更小的部分吗？                                 │
│    - 有什么模式或规律吗？                                           │
│                                                                     │
│ 3. 执行：                                                           │
│    - 逐步应用选择的策略                                             │
│    - 检查每一步的合理性                                             │
│                                                                     │
│ 4. 验证：                                                           │
│    - 答案有意义吗？                                                 │
│    - 我可以用不同的方法验证吗？                                     │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

### 决策制定启发式

```
┌─────────────────────────────────────────────────────────────────────┐
│ 决策制定提示程序                                                    │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│ 1. 框架设定：                                                       │
│    - 决策是什么？                                                   │
│    - 利益相关者是谁？                                               │
│    - 约束条件是什么？                                               │
│                                                                     │
│ 2. 选项生成：                                                       │
│    - 有哪些可能的选择？                                             │
│    - 我遗漏了任何创造性的选项吗？                                   │
│                                                                     │
│ 3. 评估标准：                                                       │
│    - 什么因素最重要？                                               │
│    - 如何权衡不同的考虑因素？                                       │
│                                                                     │
│ 4. 后果分析：                                                       │
│    - 每个选项的可能结果是什么？                                     │
│    - 风险和机会是什么？                                             │
│                                                                     │
│ 5. 选择和理由：                                                     │
│    - 基于分析，最佳选择是什么？                                     │
│    - 为什么这个选择优于其他选择？                                   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

## 认知工具2：上下文图式

图式是组织知识的心理框架。在上下文工程中，我们可以创建标准化的信息组织模式。

### 分析图式

```
┌─────────────────────────────────────────────────────────────────────┐
│ 分析上下文图式                                                      │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│ 主题：[要分析的主题]                                                │
│                                                                     │
│ 背景：                                                              │
│ • 相关历史                                                          │
│ • 当前状况                                                          │
│ • 关键利益相关者                                                    │
│                                                                     │
│ 关键因素：                                                          │
│ • 因素1：[描述和影响]                                               │
│ • 因素2：[描述和影响]                                               │
│ • 因素3：[描述和影响]                                               │
│                                                                     │
│ 数据和证据：                                                        │
│ • 定量数据：[数字、统计、趋势]                                      │
│ • 定性见解：[观察、反馈、案例研究]                                  │
│                                                                     │
│ 模式和趋势：                                                        │
│ • 观察到的模式                                                      │
│ • 新兴趋势                                                          │
│ • 异常或例外                                                        │
│                                                                     │
│ 结论：                                                              │
│ • 主要发现                                                          │
│ • 影响                                                              │
│ • 建议的行动                                                        │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

### 创意生成图式

```
┌─────────────────────────────────────────────────────────────────────┐
│ 创意生成上下文图式                                                  │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│ 挑战：[要解决的问题或机会]                                          │
│                                                                     │
│ 约束条件：                                                          │
│ • 资源限制                                                          │
│ • 时间限制                                                          │
│ • 技术限制                                                          │
│ • 其他约束                                                          │
│                                                                     │
│ 灵感来源：                                                          │
│ • 类似问题的解决方案                                                │
│ • 不同领域的方法                                                    │
│ • 自然界的例子                                                      │
│ • 历史先例                                                          │
│                                                                     │
│ 创意生成技术：                                                      │
│ • 头脑风暴变体                                                      │
│ • 类比思维                                                          │
│ • 反向思维                                                          │
│ • 随机刺激                                                          │
│                                                                     │
│ 创意评估：                                                          │
│ • 可行性                                                            │
│ • 原创性                                                            │
│ • 影响潜力                                                          │
│ • 实施复杂性                                                        │
│                                                                     │
│ 精选创意：                                                          │
│ • 创意1：[描述和理由]                                               │
│ • 创意2：[描述和理由]                                               │
│ • 创意3：[描述和理由]                                               │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

## 认知工具3：元认知和元提示

元认知是"对思维的思考" — 意识到和理解自己的思维过程。在LLM中，元提示指导模型如何思考和推理。

### 自我反思元提示

```
┌─────────────────────────────────────────────────────────────────────┐
│ 自我反思元提示                                                      │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│ 在回应之前，请考虑：                                                │
│                                                                     │
│ 1. 理解检查：                                                       │
│    - 我是否完全理解了问题？                                         │
│    - 是否有任何歧义需要澄清？                                       │
│                                                                     │
│ 2. 知识评估：                                                       │
│    - 我对这个主题了解多少？                                         │
│    - 我的知识有什么限制？                                           │
│    - 我应该表达什么程度的确定性？                                   │
│                                                                     │
│ 3. 方法选择：                                                       │
│    - 解决这个问题的最佳方法是什么？                                 │
│    - 我应该使用什么推理策略？                                       │
│                                                                     │
│ 4. 偏见检查：                                                       │
│    - 我可能有什么偏见影响我的回应？                                 │
│    - 我是否考虑了多个角度？                                         │
│                                                                     │
│ 5. 质量控制：                                                       │
│    - 我的推理是否合理？                                             │
│    - 我的回应是否有用和相关？                                       │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```
