# 提示编程：通过类代码模式的结构化推理

> "我的语言的界限意味着我的世界的界限。" — 路德维希·维特根斯坦

## 代码与提示的融合
如果我们的世界现在受到语言的限制，那么接下来会是什么，如果不是语言本身的进化？

在我们的上下文工程之旅中，我们已经从原子发展到认知工具。现在我们探索一个强大的综合：**上下文和提示编程**——一种将编程模式带入提示世界的混合方法。

```
┌──────────────────────────────────────────────────────────────────────────┐
│                                                                          │
│                        提示编程                                          │
│                                                                          │
│  ┌───────────────────┐                    ┌───────────────────┐          │
│  │                   │                    │                   │          │
│  │  编程             │                    │  提示             │          │
│  │  范式             │                    │  技术             │          │
│  │                   │                    │                   │          │
│  └───────────────────┘                    └───────────────────┘          │
│           │                                        │                     │
│           │                                        │                     │
│           ▼                                        ▼                     │
│  ┌──────────────────────────────────────────────────────────────────┐    │
│  │                                                                  │    │
│  │              结构化推理框架                                      │    │
│  │                                                                  │    │
│  └──────────────────────────────────────────────────────────────────┘    │
│                                                                          │
└──────────────────────────────────────────────────────────────────────────┘
```

正如[IBM 2025年6月](https://www.arxiv.org/pdf/2506.12115)的最新研究所强调的，提示模板可以作为认知工具或"提示程序"，显著增强推理，类似于人类启发式方法（心理捷径）。提示编程利用了两个世界的力量：编程的结构化推理和提示的灵活自然语言。

## 为什么提示编程有效

提示编程之所以有效，是因为它帮助语言模型通过遵循结构化模式来执行复杂推理，类似于编程语言指导计算的方式：

```
┌─────────────────────────────────────────────────────────────────────┐
│ 提示编程的好处                                                      │
├─────────────────────────────────────────────────────────────────────┤
│ ✓ 提供清晰的推理脚手架                                              │
│ ✓ 将复杂问题分解为可管理的步骤                                      │
│ ✓ 启用解决方案空间的系统性探索                                      │
│ ✓ 创建可重用的推理模式                                              │
│ ✓ 通过结构化验证减少错误                                            │
│ ✓ 在不同问题间提高一致性                                            │
└─────────────────────────────────────────────────────────────────────┘
```

## 核心概念：认知操作作为函数

提示编程的基本洞察是将认知操作视为可调用函数：

```
┌─────────────────────────────────────────────────────────────────────┐
│ 传统提示                      │ 提示编程                            │
├───────────────────────────────┼─────────────────────────────────────┤
│ "分析第一次世界大战的原因，   │ analyze(                            │
│  考虑政治、经济和社会因素。"  │   topic="第一次世界大战的原因",     │
│                               │   factors=["政治",                  │
│                               │            "经济",                  │
│                               │            "社会"],                 │
│                               │   depth="全面",                     │
│                               │   format="结构化"                   │
│                               │ )                                   │
└───────────────────────────────┴─────────────────────────────────────┘
```

虽然两种方法都能产生类似结果，但提示编程版本：
1. 使参数明确
2. 启用输入的系统性变化
3. 为类似分析创建可重用模板
4. 通过特定推理结构指导模型

## 认知工具 vs. 提示编程

提示编程代表了认知工具概念的进化：

```
┌─────────────────────────────────────────────────────────────────────┐
│ 结构化推理的进化                                                    │
│                                                                     │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐            │
│  │             │     │             │     │             │            │
│  │ 提示        │────►│ 认知        │────►│ 提示        │            │
│  │             │     │ 工具        │     │ 编程        │            │
│  │             │     │             │     │             │            │
│  └─────────────┘     └─────────────┘     └─────────────┘            │
│                                                                     │
│  "第一次世界大战    "将分析工具        "analyze({                   │
│   的原因是什么？"    应用于第一次       topic: '第一次世界大战',     │
│                     世界大战"          framework: '因果',           │
│                                        depth: '全面'                │
│                                       })"                           │
└─────────────────────────────────────────────────────────────────────┘
```

## 提示中的关键编程范式

提示编程借鉴了各种编程范式：

### 1. 函数式编程

```
┌─────────────────────────────────────────────────────────────────────┐
│ 函数式编程模式                                                      │
├─────────────────────────────────────────────────────────────────────┤
│ function analyze(topic, factors, depth) {                           │
│   // 基于参数执行分析                                               │
│   return structured_analysis;                                       │
│ }                                                                   │
│                                                                     │
│ function summarize(text, length, focus) {                           │
│   // 使用指定参数生成摘要                                           │
│   return summary;                                                   │
│ }                                                                   │
│                                                                     │
│ // 函数组合                                                         │
│ result = summarize(analyze("气候变化", ["经济",                     │
│                                        "环境"],                     │
│                           "详细"),                                  │
│                   "简要", "影响");                                  │
└─────────────────────────────────────────────────────────────────────┘
```

### 2. 过程式编程

```
┌─────────────────────────────────────────────────────────────────────┐
│ 过程式编程模式                                                      │
├─────────────────────────────────────────────────────────────────────┤
│ procedure solveEquation(equation) {                                 │
│   step 1: 识别方程类型                                             │
│   step 2: 应用适当的求解方法                                       │
│   step 3: 检查解的有效性                                           │
│   step 4: 返回解                                                   │
│ }                                                                   │
│                                                                     │
│ procedure analyzeText(text) {                                       │
│   step 1: 识别主要主题                                             │
│   step 2: 提取关键论点                                             │
│   step 3: 评估证据质量                                             │
│   step 4: 综合发现                                                 │
│ }                                                                   │
└─────────────────────────────────────────────────────────────────────┘
```

### 3. 面向对象编程

```
┌─────────────────────────────────────────────────────────────────────┐
│ 面向对象编程模式                                                    │
├─────────────────────────────────────────────────────────────────────┤
│ class TextAnalyzer {                                                │
│   properties:                                                       │
│     - text: 要分析的内容                                           │
│     - language: 文本语言                                           │
│     - focus_areas: 要分析的方面                                    │
│                                                                     │
│   methods:                                                          │
│     - identifyThemes(): 找到主要主题                               │
│     - extractEntities(): 识别人物、地点等                          │
│     - analyzeSentiment(): 确定情感色调                             │
│     - generateSummary(): 创建简洁摘要                              │
│ }                                                                   │
│                                                                     │
│ analyzer = new TextAnalyzer(                                        │
│   text="文章内容...",                                              │
│   language="中文",                                                 │
│   focus_areas=["主题", "情感"]                                     │
│ )                                                                   │
│                                                                     │
│ themes = analyzer.identifyThemes()                                  │
│ sentiment = analyzer.analyzeSentiment()                             │
└─────────────────────────────────────────────────────────────────────┘
```

## 实现提示编程

让我们探索提示编程的实际实现：

### 1. 基本函数定义和调用

```
# 定义认知函数
function summarize(text, length="short", style="informative", focus=null) {
  // 函数描述
  // 使用指定参数总结提供的文本
  
  // 参数验证
  if (length not in ["short", "medium", "long"]) {
    throw Error("长度必须是short、medium或long");
  }
  
  // 处理逻辑
  summary_length = {
    "short": "1-2段",
    "medium": "3-4段",
    "long": "5+段"
  }[length];
  
  focus_instruction = focus ? 
    `特别关注与${focus}相关的方面。` : 
    "均匀涵盖所有要点。";
  
  // 输出规范
  return `
    任务：总结以下文本。
    
    参数：
    - 长度：${summary_length}
    - 风格：${style}
    - 特殊指示：${focus_instruction}
    
    要总结的文本：
    ${text}
    
    请提供文本的${style}摘要，长度为${summary_length}。
    ${focus_instruction}
  `;
}

# 调用函数
input_text = "关于气候变化的长文章...";
summarize(input_text, length="medium", focus="经济影响");
```

### 2. 函数组合

```
# 定义多个认知函数
function research(topic, depth="comprehensive", sources=5) {
  // 函数实现
  return `使用${sources}个来源以${depth}深度研究关于${topic}的信息。`;
}

function analyze(information, framework="thematic", perspective="neutral") {
  // 函数实现
  return `使用${framework}框架从${perspective}视角分析以下信息：${information}`;
}

function synthesize(analysis, format="essay", tone="academic") {
  // 函数实现
  return `将以下分析综合成${tone}语调的${format}：${analysis}`;
}

# 为复杂任务组合函数
topic = "人工智能对就业的影响";
research_results = research(topic, depth="详细", sources=8);
analysis_results = analyze(research_results, framework="因果效应", perspective="平衡");
final_output = synthesize(analysis_results, format="报告", tone="专业");
```

### 3. 条件逻辑和控制流

```
function solve_math_problem(problem, show_work=true, check_solution=true) {
  // 确定问题类型
  if contains_variables(problem) {
    approach = "代数";
    steps = [
      "识别变量和常数",
      "建立方程",
      "求解未知变量",
      "在原问题中验证解"
    ];
  } else if contains_geometry_terms(problem) {
    approach = "几何";
    steps = [
      "识别相关几何性质",
      "应用适当的几何公式",
      "计算所需值",
      "验证解的一致性"
    ];
  } else {
    approach = "算术";
    steps = [
      "将计算分解为步骤",
      "按正确顺序执行操作",
      "计算最终结果",
      "验证计算"
    ];
  }

  // 构建提示
  prompt = `
    任务：解决以下${approach}问题。

    问题：${problem}

    ${show_work ? "按照这种方法逐步显示你的工作：" : "只提供最终答案。"}
    ${show_work ? steps.map((step, i) => `${i+1}. ${step}`).join("\n") : ""}

    ${check_solution ? "解决后，通过检查是否满足原问题中的所有条件来验证你的答案。" : ""}
  `;

  return prompt;
}

// 示例用法
problem = "如果3x + 7 = 22，求x的值。";
solve_math_problem(problem, show_work=true, check_solution=true);
```

### 4. 迭代完善循环

```
function iterative_essay_writing(topic, iterations=3) {
  // 初始草稿
  draft = `写一篇关于${topic}的基本初稿文章。专注于把主要想法写下来。`;

  // 完善循环
  for (i = 1; i <= iterations; i++) {
    if (i == 1) {
      // 第一次完善：结构和内容
      draft = `
        审查以下文章草稿：

        ${draft}

        通过以下具体改变改进结构和内容：
        1. 在引言中添加清晰的论点陈述
        2. 确保每段都有主题句
        3. 为每个要点添加支持证据
        4. 在段落间创建更流畅的过渡

        提供修订后的文章。
      `;
    } else if (i == 2) {
      // 第二次完善：语言和风格
      draft = `
        审查以下文章：

        ${draft}

        通过以下改变改进语言和风格：
        1. 在适当的地方消除被动语态
        2. 用更具体的术语替换通用术语
        3. 变化句子结构和长度
        4. 删除冗余和填充短语

        提供修订后的文章。
      `;
    } else {
      // 最终完善：润色和定稿
      draft = `
        审查以下文章：

        ${draft}

        进行最终改进：
        1. 确保结论有效总结关键点
        2. 检查整篇文章的逻辑流程
        3. 验证文章完全涵盖主题
        4. 添加引人注目的最终思考

        提供最终润色的文章。
      `;
    }
  }

  return draft;
}

// 示例用法
essay_prompt = iterative_essay_writing("人工智能对现代医疗保健的影响", iterations=3);
```

## 认知工具与提示编程的整合

提示编程最强大的应用之一是创建"认知工具"——封装特定推理操作的专门函数：

```
┌───────────────────────────────────────────────────────────────────────────┐
│                     认知工具库                                            │
│                                                                           │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐        │
│  │                 │    │                 │    │                 │        │
│  │ understand      │    │ recall_related  │    │ examine_answer  │        │
│  │ question        │    │                 │    │                 │        │
│  │                 │    │                 │    │                 │        │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘        │
│                                                                           │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐        │
│  │                 │    │                 │    │                 │        │
│  │ backtracking    │    │ step_by_step    │    │ verify_logic    │        │
│  │                 │    │                 │    │                 │        │
│  │                 │    │                 │    │                 │        │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘        │
│                                                                           │
└───────────────────────────────────────────────────────────────────────────┘
```

正如Brown等人（2025）所概述的，这些认知工具可以在提示程序中调用以结构化复杂推理：

```python
function solve_complex_problem(problem) {
  // 首先，确保我们正确理解问题
  understanding = understand_question(problem);

  // 回忆相关知识或例子
  related_knowledge = recall_related(problem, limit=2);

  // 尝试逐步解决
  solution_attempt = step_by_step(problem, context=[understanding, related_knowledge]);

  // 验证解决方案
  verification = verify_logic(solution_attempt);

  // 如果验证失败，尝试回溯
  if (!verification.is_correct) {
    revised_solution = backtracking(solution_attempt, error_points=verification.issues);
    return revised_solution;
  }

  return solution_attempt;
}

// 认知工具的示例实现
function understand_question(question) {
  return `
    任务：分析并分解以下问题。

    问题：${question}

    请提供：
    1. 被问的核心任务
    2. 需要解决的关键组件
    3. 任何隐含假设
    4. 要考虑的约束或条件
    5. 问题的清晰重述
  `;
}
```

## 实现完整的提示程序

让我们为数学推理实现一个完整的提示程序：

```python
// 定义我们的认知工具
function understand_math_problem(problem) {
  return `
    任务：在解决之前彻底分析这个数学问题。

    问题：${problem}

    请提供：
    1. 这是什么类型的数学问题？（代数、几何、微积分等）
    2. 关键变量或未知数是什么？
    3. 给定的值或约束是什么？
    4. 问题具体要求什么？
    5. 哪些公式或方法将相关？
  `;
}

function plan_solution_steps(problem_analysis) {
  return `
    任务：创建解决这个数学问题的逐步计划。

    问题分析：${problem_analysis}

    请概述解决这个问题的具体步骤序列。
    对于每一步：
    1. 将应用什么操作或方法
    2. 这一步将完成什么
    3. 这一步的预期结果是什么

    清楚地格式化每一步并按顺序编号。
  `;
}

function execute_solution(problem, solution_plan) {
  return `
    任务：按照提供的计划解决这个数学问题。

    问题：${problem}

    解决计划：${solution_plan}

    请显示每一步的所有工作：
    - 写出所有方程
    - 显示所有计算
    - 解释每一步的推理
    - 突出中间结果

    完成所有步骤后，清楚地陈述最终答案。
  `;
}

function verify_solution(problem, solution) {
  return `
    任务：验证这个数学解决方案的正确性。

    原始问题：${problem}

    提议的解决方案：${solution}

    请检查：
    1. 所有计算是否正确？
    2. 是否使用了适当的公式和方法？
    3. 最终答案是否真正解决了原始问题？
    4. 是否有任何逻辑错误或遗漏的约束？

    如果你发现任何错误，请清楚地解释。如果解决方案正确，
    请确认这一点并解释你如何验证的。
  `;
}

// 主要问题解决函数
function solve_math_with_cognitive_tools(problem) {
  // 步骤1：理解问题
  problem_analysis = LLM(understand_math_problem(problem));

  // 步骤2：计划解决方法
  solution_plan = LLM(plan_solution_steps(problem_analysis));

  // 步骤3：执行解决方案
  detailed_solution = LLM(execute_solution(problem, solution_plan));

  // 步骤4：验证解决方案
  verification = LLM(verify_solution(problem, detailed_solution));

  // 步骤5：返回完整推理过程
  return {
    original_problem: problem,
    analysis: problem_analysis,
    plan: solution_plan,
    solution: detailed_solution,
    verification: verification
  };
}

// 示例用法
problem = "一个矩形花园的周长是36米。如果宽度是6米，花园的长度是多少？";
solve_math_with_cognitive_tools(problem);
```
```
