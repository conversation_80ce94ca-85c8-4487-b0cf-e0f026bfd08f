# 00_toy_chatbot: Simple Demonstration Agent

A minimal implementation demonstrating context engineering principles from atoms to meta-recursive operations.

## Overview

This toy chatbot showcases the progression through context engineering layers:
- **Atoms**: Basic prompts and responses
- **Molecules**: Context combinations and examples  
- **Cells**: Memory and state management
- **Organs**: Coordinated system behaviors
- **Fields**: Continuous semantic operations
- **Meta-Recursive**: Self-improvement capabilities

## Architecture

```
Context Field Architecture:
├── Core Layer: Basic conversation handling
├── Protocol Layer: Field operations and resonance
├── Memory Layer: Persistent attractor dynamics
├── Meta Layer: Self-reflection and improvement
└── Integration: Unified field orchestration
```

## Implementation Strategy

**Phase 1: Atomic Foundation**
- Basic prompt-response patterns
- Simple conversation flow

**Phase 2: Field Integration** 
- Protocol shell implementations
- Context field management
- Attractor dynamics

**Phase 3: Meta-Recursive Enhancement**
- Self-monitoring capabilities
- Protocol adaptation
- Emergent behavior detection

## Protocol Shells Used

- `/attractor.co.emerge`: Context pattern detection and surfacing
- `/field.resonance.scaffold`: Conversation coherence maintenance  
- `/recursive.memory.attractor`: Memory persistence across sessions
- `/field.self_repair`: Error recovery and adaptation

## Files

1. `chatbot_core.py` - Core implementation with field operations
2. `protocol_shells.py` - Protocol shell implementations
3. `context_field.py` - Context field management
4. `conversation_examples.py` - Demonstration conversations
5. `meta_recursive_demo.py` - Self-improvement demonstration

## Usage

```python
from chatbot_core import ToyContextChatbot

# Initialize with field protocols
chatbot = ToyContextChatbot()

# Demonstrate basic conversation
response = chatbot.chat("Hello, how are you?")

# Show field operations
chatbot.show_field_state()

# Demonstrate meta-recursive improvement
chatbot.meta_improve()
```

## Demonstration Goals

1. **Show Progression**: From simple responses to sophisticated field operations
2. **Validate Protocols**: Demonstrate protocol shell effectiveness
3. **Measure Coherence**: Show field coherence and resonance metrics
4. **Meta-Recursive**: Self-improvement and adaptation capabilities

This implementation serves as a concrete example of how context engineering principles create more sophisticated and adaptive conversational systems.
